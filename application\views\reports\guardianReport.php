<?php
$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
?>
<style type="text/css">
    /*REQUIRED*/
    .carousel-row {
        margin-bottom: 10px;
    }
    .slide-row {
        padding: 0;
        background-color: #ffffff;
        min-height: 150px;
        border: 1px solid #e7e7e7;
        overflow: hidden;
        height: auto;
        position: relative;
    }
    .slide-carousel {
        width: 20%;
        float: left;
        display: inline-block;
    }
    .slide-carousel .carousel-indicators {
        margin-bottom: 0;
        bottom: 0;
        background: rgba(0, 0, 0, .5);
    }
    .slide-carousel .carousel-indicators li {
        border-radius: 0;
        width: 20px;
        height: 6px;
    }
    .slide-carousel .carousel-indicators .active {
        margin: 1px;
    }
    .slide-content {
        position: absolute;
        top: 0;
        left: 20%;
        display: block;
        float: left;
        width: 80%;
        max-height: 76%;
        padding: 1.5% 2% 2% 2%;
        overflow-y: auto;
    }
    .slide-content h4 {
        margin-bottom: 3px;
        margin-top: 0;
    }
    .slide-footer {
        position: absolute;
        bottom: 0;
        left: 20%;
        width: 78%;
        height: 20%;
        margin: 1%;
    }
    /* Scrollbars */
    .slide-content::-webkit-scrollbar {
        width: 5px;
    }
    .slide-content::-webkit-scrollbar-thumb:vertical {
        margin: 5px;
        background-color: #999;
        -webkit-border-radius: 5px;
    }
    .slide-content::-webkit-scrollbar-button:start:decrement,
    .slide-content::-webkit-scrollbar-button:end:increment {
        height: 5px;
        display: block;
    }
</style>

<div class="content-wrapper" style="min-height: 946px;">
    <section class="content-header">
        <h1>
            <i class="fa fa-user-plus"></i> <?php //echo $this->lang->line('student_information'); ?>
        </h1>
    </section>
    <!-- Main content -->
    <section class="content">
        <?php $this->load->view('reports/_studentinformation');?>
        <div class="row">
            <div class="col-md-12">
                <div class="box removeboxmius">
                    <div class="box-header ptbnull"></div>
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
                    </div>
                    <div class="box-body">
                        <form role="form" id="reportform" action="<?php echo site_url('report/guardiansearchvalidation') ?>" method="post" class="">
                            <div class="row">
                                <?php echo $this->customlib->getCSRF(); ?>
                                <div class="col-sm-6 col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('class'); ?></label>
                                        <select id="class_id" name="class_id[]" class="form-control multiselect-dropdown" multiple>
                                            <?php
foreach ($classlist as $class) {
    ?>
                                                <option value="<?php echo $class['id'] ?>" <?php if (set_value('class_id') == $class['id']) {
        echo "selected=selected";
    }
    ?>><?php echo $class['class'] ?></option>
                                                <?php
$count++;
}
?>
                                        </select>
                                        <span class="text-danger" id="error_class_id"></span>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('section'); ?></label>
                                        <select id="section_id" name="section_id[]" class="form-control multiselect-dropdown" multiple>
                                        </select>
                                        <span class="text-danger" id="error_section_id"></span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <button type="button" id="test-selection" class="btn btn-info btn-sm" style="margin-right: 10px;">Test Selection</button>
                                        <button type="submit" name="search" value="search_filter" class="btn btn-primary btn-sm checkbox-toggle pull-right"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                                    </div>
                                </div>
                            </div><!--./row-->
                        </form>
                    </div><!--./box-body-->
                    <div class="">
                        <div class="box-header ptbnull"></div>
                        <div class="box-header ptbnull">
                            <h3 class="box-title titlefix"><i class="fa fa-users"></i> <?php echo form_error('student'); ?> <?php echo $this->lang->line('guardian_report'); ?></h3>
                        </div>
                        <div class="box-body table-responsive">
                            <div class="download_label"><?php
echo $this->lang->line('guardian_report') . "<br>";
$this->customlib->get_postmessage();
?></div>
                            <table class="table table-striped table-bordered table-hover" id="guardian-list">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('class_section'); ?></th>
                                        <th><?php echo $this->lang->line('admission_no'); ?></th>
                                        <th><?php echo $this->lang->line('student_name'); ?></th>
                                        <?php if ($sch_setting->mobile_no) {?>
                                            <th><?php echo $this->lang->line('mobile_number'); ?></th>
                                        <?php }if ($sch_setting->guardian_name) {?>
                                        <th><?php echo $this->lang->line('guardian_name'); ?></th>
                                        <?php }if ($sch_setting->guardian_relation) {?>
                                            <th><?php echo $this->lang->line('guardian_relation'); ?></th>
                                        <?php }if ($sch_setting->guardian_phone) {?>
                                        <th><?php echo $this->lang->line('guardian_phone'); ?></th>
                                        <?php }if ($sch_setting->father_name) {?>
                                            <th><?php echo $this->lang->line('father_name'); ?></th>
                                        <?php }if ($sch_setting->father_phone) {?>
                                            <th><?php echo $this->lang->line('father_phone'); ?></th>
                                        <?php }if ($sch_setting->mother_name) {?>
                                            <th><?php echo $this->lang->line('mother_name'); ?></th>
                                        <?php }if ($sch_setting->mother_phone) {?>
                                            <th><?php echo $this->lang->line('mother_phone'); ?></th>
<?php }?>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div><!--./box box-primary -->
            </div><!-- ./col-md-12 -->
        </div>
</div>
</section>
</div>

<!-- Include SumoSelect CSS and JS -->
<link href="<?php echo base_url(); ?>assets1/vendor/bootstrap-multiselect/sumoselect.min.css" rel="stylesheet"/>
<script src="<?php echo base_url(); ?>assets1/vendor/bootstrap-multiselect/jquery.sumoselect.min.js"></script>

<style>
/* Multi-select dropdown enhancements */
.SumoSelect {
    width: 100% !important;
}

.SumoSelect > .CaptionCont {
    border: 1px solid #d2d6de;
    border-radius: 3px;
    background-color: #fff;
    min-height: 34px;
    padding: 6px 12px;
}

.SumoSelect > .CaptionCont > span {
    line-height: 1.42857143;
    color: #555;
    padding-right: 20px;
}

.SumoSelect > .CaptionCont > span.placeholder {
    color: #999;
    font-style: italic;
}

.SumoSelect.open > .CaptionCont,
.SumoSelect:focus > .CaptionCont,
.SumoSelect:hover > .CaptionCont {
    border-color: #66afe9;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
}

.SumoSelect .optWrapper {
    border: 1px solid #d2d6de;
    border-radius: 3px;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
    background-color: #fff;
    z-index: 9999 !important;
    position: absolute !important;
}

.SumoSelect .optWrapper ul.options {
    max-height: 200px;
    overflow-y: auto;
}

.SumoSelect .optWrapper ul.options li {
    padding: 8px 12px;
    border-bottom: 1px solid #f4f4f4;
    cursor: pointer !important;
    user-select: none;
}

.SumoSelect .optWrapper ul.options li:hover {
    background-color: #f5f5f5;
}

.SumoSelect .optWrapper ul.options li.selected {
    background-color: #337ab7;
    color: #fff;
}

/* Ensure dropdown items are clickable */
.SumoSelect .optWrapper ul.options li label {
    cursor: pointer !important;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
}

.SumoSelect .optWrapper ul.options li input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer !important;
}

.SumoSelect .search-txt {
    border: 1px solid #d2d6de;
    border-radius: 3px;
    padding: 6px 12px;
    margin: 5px;
    width: calc(100% - 10px);
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .col-sm-6.col-md-6 {
        margin-bottom: 15px;
    }

    .SumoSelect > .CaptionCont {
        min-height: 40px;
        padding: 8px 12px;
    }

    .form-group label {
        font-weight: 600;
        margin-bottom: 5px;
    }
}

@media (max-width: 480px) {
    .SumoSelect > .CaptionCont {
        min-height: 44px;
        padding: 10px 12px;
    }
}

/* Form styling improvements */
.form-group label {
    margin-bottom: 5px;
    font-weight: 500;
}

/* Select all/clear all button styling */
.SumoSelect .select-all {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 8px 12px;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: block !important;
}

.SumoSelect .select-all:hover {
    background-color: #e9ecef;
}

/* Ensure Select All option is visible */
.SumoSelect .optWrapper .options li.opt {
    display: list-item !important;
    padding: 6px 12px;
    cursor: pointer;
}

.SumoSelect .optWrapper .options li.opt:hover {
    background-color: #f5f5f5;
}

/* Select All specific styling */
.SumoSelect .optWrapper .options li.opt.select-all {
    background-color: #e3f2fd;
    border-bottom: 1px solid #bbdefb;
    font-weight: 600;
    color: #1976d2;
}

.SumoSelect .optWrapper .options li.opt.select-all:hover {
    background-color: #bbdefb;
}

/* Loading state for dropdowns */
.SumoSelect.loading > .CaptionCont {
    opacity: 0.6;
    pointer-events: none;
}

.SumoSelect.loading > .CaptionCont:after {
    content: "";
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -8px;
    width: 16px;
    height: 16px;
    border: 2px solid #ccc;
    border-top-color: #337ab7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>

<script type="text/javascript">
    $(document).ready(function () {
        console.log('Document ready, jQuery version:', $.fn.jquery);
        console.log('jQuery object:', typeof $);
        console.log('Found multiselect dropdowns:', $('.multiselect-dropdown').length);
        console.log('Window jQuery versions:', window.jQuery ? window.jQuery.fn.jquery : 'none');

        // Check for jQuery conflicts
        if (typeof window.jQuery !== 'undefined' && window.jQuery.fn.jquery !== $.fn.jquery) {
            console.warn('Multiple jQuery versions detected! This may cause conflicts.');
            console.log('Current $:', $.fn.jquery);
            console.log('Window jQuery:', window.jQuery.fn.jquery);
        }

        // Check if SumoSelect is available
        console.log('Checking SumoSelect availability...');
        console.log('jQuery version:', $.fn.jquery);
        console.log('SumoSelect function exists:', typeof $.fn.SumoSelect);
        console.log('Available jQuery plugins:', Object.keys($.fn).filter(function(key) { return key.toLowerCase().includes('sumo') || key.toLowerCase().includes('select'); }));

        if (typeof $.fn.SumoSelect === 'undefined') {
            console.error('SumoSelect plugin not loaded!');
            console.log('Trying to load SumoSelect manually...');

            // Check if the script tag exists
            var sumoScripts = $('script[src*="sumoselect"]');
            console.log('SumoSelect script tags found:', sumoScripts.length);
            sumoScripts.each(function() {
                console.log('Script src:', $(this).attr('src'));
            });

            // Check if CSS is loaded
            var sumoCss = $('link[href*="sumoselect"]');
            console.log('SumoSelect CSS links found:', sumoCss.length);
            sumoCss.each(function() {
                console.log('CSS href:', $(this).attr('href'));
            });

            console.log('Falling back to Bootstrap Select...');

            // Try Bootstrap Select as fallback
            if (typeof $.fn.selectpicker !== 'undefined') {
                console.log('Bootstrap Select is available, using as fallback');
                $('.multiselect-dropdown').addClass('selectpicker').attr('data-live-search', 'true').attr('data-actions-box', 'true');
                $('.multiselect-dropdown').selectpicker({
                    noneSelectedText: 'Select Options',
                    countSelectedText: '{0} Selected',
                    selectAllText: 'Select All',
                    deselectAllText: 'Clear All',
                    liveSearch: true,
                    actionsBox: true
                });
                console.log('Bootstrap Select initialized as fallback');
                return;
            } else {
                console.log('Bootstrap Select not available, using basic styling');
                // Apply basic multi-select styling as final fallback
                $('.multiselect-dropdown').each(function() {
                    var $this = $(this);
                    $this.css({
                        'height': 'auto',
                        'min-height': '34px',
                        'border': '1px solid #d2d6de',
                        'border-radius': '3px',
                        'padding': '6px 12px'
                    });
                });
            }

            return;
        } else {
            console.log('✓ SumoSelect plugin is available');
        }

        // Add a small delay to ensure DOM is fully ready
        setTimeout(function() {
            console.log('Initializing SumoSelect after timeout...');
            console.log('Available dropdowns:', $('.multiselect-dropdown').map(function() { return this.id; }).get());

            // Test if we can access SumoSelect directly
            console.log('Testing direct SumoSelect access...');
            var testElement = $('#class_id');
            console.log('Test element found:', testElement.length);
            console.log('Test element is select:', testElement.is('select'));
            console.log('Test element has multiple:', testElement.attr('multiple'));

            // Try to call SumoSelect directly on test element
            try {
                console.log('Attempting direct SumoSelect call...');
                testElement.SumoSelect();
                console.log('Direct SumoSelect call successful!');
            } catch (directError) {
                console.error('Direct SumoSelect call failed:', directError);
            }

            // Test basic jQuery functionality first
            $('.multiselect-dropdown').each(function() {
                var $this = $(this);
                var id = $this.attr('id');
                console.log('Testing dropdown:', id);
                console.log('- Element exists:', $this.length > 0);
                console.log('- Is select element:', $this.is('select'));
                console.log('- Has multiple attribute:', $this.attr('multiple') !== undefined);
                console.log('- Options count:', $this.find('option').length);
                console.log('- Is visible:', $this.is(':visible'));
            });

            // Initialize SumoSelect for all multi-select dropdowns
            console.log('Starting SumoSelect initialization...');

            $('.multiselect-dropdown').each(function(index) {
                var $this = $(this);
                var id = $this.attr('id');
                console.log('Processing dropdown #' + index + ':', id);
                console.log('- Element type:', $this.prop('tagName'));
                console.log('- Has multiple attribute:', $this.prop('multiple'));
                console.log('- Options count:', $this.find('option').length);
                console.log('- Is visible:', $this.is(':visible'));

                // Simple initialization
                try {
                    $this.SumoSelect({
                        placeholder: 'Select Options',
                        csvDispCount: 3,
                        captionFormat: '{0} Selected',
                        captionFormatAllSelected: 'All Selected ({0})',
                        selectAll: true,
                        search: true,
                        searchText: 'Search...',
                        noMatch: 'No matches found',
                        okCancelInMulti: true,
                        isClickAwayOk: true
                    });

                    console.log('✓ SumoSelect initialized for:', id);
                    console.log('- Sumo object created:', !!$this[0].sumo);
                    console.log('- SumoSelect wrapper created:', $('.SumoSelect').length);

                } catch (error) {
                    console.error('✗ Failed to initialize SumoSelect for', id, ':', error);
                }
            });

            console.log('SumoSelect initialization complete');

            // Test dropdown functionality
            $('.multiselect-dropdown').each(function() {
                var $this = $(this);
                var id = $this.attr('id');

                // Add click event listener to test
                $this.on('sumo:opened', function() {
                    console.log('Dropdown opened:', id);
                });

                $this.on('sumo:closed', function() {
                    console.log('Dropdown closed:', id);
                });

                $this.on('sumo:selection-changed', function() {
                    console.log('Selection changed:', id, 'Selected values:', $this.val());
                });
            });

            // Add test button functionality
            $('#test-selection').click(function() {
                console.log('=== TESTING DROPDOWN SELECTION ===');
                $('.multiselect-dropdown').each(function() {
                    var $this = $(this);
                    var id = $this.attr('id');
                    console.log('Dropdown:', id);
                    console.log('- Current value:', $this.val());
                    console.log('- Has SumoSelect:', !!$this[0].sumo);
                    console.log('- SumoSelect wrapper exists:', $('.SumoSelect').length);

                    if ($this[0].sumo) {
                        try {
                            console.log('- SumoSelect methods available:', Object.keys($this[0].sumo));
                        } catch (e) {
                            console.log('- Error accessing SumoSelect methods:', e.message);
                        }
                    }
                });

                // Try to programmatically select first option in class dropdown
                var classDropdown = $('#class_id');
                var firstOption = classDropdown.find('option:first').val();
                if (firstOption) {
                    console.log('Trying to select first option:', firstOption);
                    classDropdown.val([firstOption]);
                    classDropdown.trigger('change');
                    console.log('After selection, value is:', classDropdown.val());
                }
            });

            // Initialize section dropdown on page load if class is pre-selected
            var preSelectedClass = $('#class_id').val();
            if (preSelectedClass && preSelectedClass.length > 0) {
                $('#class_id').trigger('change');
            }
        }, 100); // 100ms delay

        $(document).on('change', '#class_id', function (e) {
            var sectionDropdown = $('#section_id')[0];
            if (sectionDropdown && sectionDropdown.sumo) {
                sectionDropdown.sumo.removeAll();
            }

            var class_ids = $(this).val();
            var base_url = '<?php echo base_url() ?>';

            if (class_ids && class_ids.length > 0) {
                var requests = [];

                // Get sections for all selected classes
                $.each(class_ids, function(index, class_id) {
                    requests.push(
                        $.ajax({
                            type: "GET",
                            url: base_url + "sections/getByClass",
                            data: {'class_id': class_id},
                            dataType: "json"
                        })
                    );
                });

                // Wait for all requests to complete
                $.when.apply($, requests).done(function() {
                    var allSections = [];
                    var addedSections = {};

                    // Process results from all requests
                    for (var i = 0; i < arguments.length; i++) {
                        var data = requests.length === 1 ? arguments[0] : arguments[i][0];
                        if (data && Array.isArray(data)) {
                            $.each(data, function (j, obj) {
                                if (!addedSections[obj.section_id]) {
                                    allSections.push(obj);
                                    addedSections[obj.section_id] = true;
                                }
                            });
                        }
                    }

                    // Add options to section dropdown
                    if (sectionDropdown && sectionDropdown.sumo) {
                        $.each(allSections, function (i, obj) {
                            sectionDropdown.sumo.add(obj.section_id, obj.section);
                        });
                    }
                });
            }
        });
    });


</script>

<script>
$(document).ready(function() {
    emptyDatatable('guardian-list','data');
});
</script>

<script type="text/javascript">
$(document).ready(function(){
$(document).on('submit','#reportform',function(e){
    e.preventDefault(); // avoid to execute the actual submit of the form.
    var $this = $(this).find("button[type=submit]:focus");
    var form = $(this);
    var url = form.attr('action');

    // Custom serialization to handle multi-select properly
    var form_data = [];

    // Get all form elements
    form.find('input, select, textarea').each(function() {
        var $element = $(this);
        var name = $element.attr('name');
        var type = $element.attr('type');

        console.log('Processing form element:', name, 'type:', type, 'is multi-select:', $element.is('select[multiple]'));

        if (name) {
            if ($element.is('select[multiple]')) {
                // Handle multi-select dropdowns
                var values = $element.val();
                console.log('Multi-select values for', name, ':', values);
                if (values && values.length > 0) {
                    $.each(values, function(index, value) {
                        form_data.push({name: name, value: value});
                    });
                }
            } else if (type !== 'submit' && type !== 'button') {
                // Handle other form elements
                console.log('Regular form element:', name, 'value:', $element.val());
                form_data.push({name: name, value: $element.val()});
            }
        }
    });

    form_data.push({name: 'search_type', value: $this.attr('value')});

    console.log('Form data being sent:', form_data);
    console.log('URL:', url);

    $.ajax({
           url: url,
           type: "POST",
           dataType:'JSON',
           data: form_data,
              beforeSend: function () {
                $('[id^=error]').html("");
                $this.button('loading');

               },
              success: function(response) { // your success handler
                console.log('AJAX response:', response);

                if(!response.status){
                    console.log('Validation errors:', response.error);
                    $.each(response.error, function(key, value) {
                    $('#error_' + key).html(value);
                    });
                }else{
                   console.log('Initializing DataTable with params:', response.params);
                   initDatatable('guardian-list','report/dtguardianreportlist',response.params,[],100);
                }
              },
             error: function() { // your error handler
                 $this.button('reset');
             },
             complete: function() {
             $this.button('reset');
             }
         });
        });
    });
</script>